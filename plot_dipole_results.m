%% ========================================================================
%  单向偶极子声波测井结果画图脚本
%  Plot Script for Unidirectional Dipole Acoustic Logging Results
%% ========================================================================
clc; clear; close all;

%% ========================================================================
%  第一部分：数据加载
%% ========================================================================

% 查找最新的结果文件
files = dir('unidirectional_dipole_acoustic_logging_*receiver_results.mat');
if isempty(files)
    error('未找到结果文件！请先运行 unidirectional_dipole_acoustic_logging.m');
end

% 选择最新的文件
[~, idx] = max([files.datenum]);
filename = files(idx).name;
fprintf('加载数据文件：%s\n', filename);

% 加载数据
load(filename);

fprintf('数据加载完成！\n');
fprintf('检波器数量：%d\n', num_receivers);
fprintf('时间步数：%d\n', nt);
fprintf('震源频率：%.0f Hz\n', f0);
if enable_das
    fprintf('DAS系统：启用，%d个标距点\n', num_das_points);
else
    fprintf('DAS系统：禁用\n');
end

%% ========================================================================
%  第二部分：检波器数据可视化
%% ========================================================================

% 图1：检波器X方向速度数据（偶极子主要响应）
figure('Name', '检波器X方向速度数据', 'Position', [100 100 1200 800]);

subplot(2,2,1);
imagesc(time_axis*1e6, source_distances, receiver_data_x');
xlabel('时间 (μs)');
ylabel('源距 (m)');
title('检波器X方向速度 (偶极子主要响应)');
colorbar;
colormap(seismic_colormap());

subplot(2,2,2);
imagesc(time_axis*1e6, source_distances, receiver_data_y');
xlabel('时间 (μs)');
ylabel('源距 (m)');
title('检波器Y方向速度');
colorbar;
colormap(seismic_colormap());

subplot(2,2,3);
imagesc(time_axis*1e6, source_distances, receiver_data_pressure');
xlabel('时间 (μs)');
ylabel('源距 (m)');
title('检波器压力数据');
colorbar;
colormap(seismic_colormap());

% 单道波形对比
subplot(2,2,4);
if num_receivers >= 3
    plot(time_axis*1e6, receiver_data_x(:,2), 'r-', 'LineWidth', 1.5); hold on;
    plot(time_axis*1e6, receiver_data_y(:,2), 'b-', 'LineWidth', 1.5);
    plot(time_axis*1e6, receiver_data_pressure(:,2)/max(abs(receiver_data_pressure(:,2)))*max(abs(receiver_data_x(:,2))), 'g-', 'LineWidth', 1.5);
    xlabel('时间 (μs)');
    ylabel('振幅');
    title(sprintf('第2个检波器波形对比 (源距: %.2fm)', source_distances(2)));
    legend('X方向速度', 'Y方向速度', '压力(归一化)', 'Location', 'best');
    grid on;
end

%% ========================================================================
%  第三部分：DAS数据可视化（如果启用）
%% ========================================================================

if enable_das
    % 图2：DAS应变率数据
    figure('Name', 'DAS应变率数据', 'Position', [150 150 1200 600]);
    
    subplot(1,2,1);
    imagesc(time_axis*1e6, source_distances, das_data');
    xlabel('时间 (μs)');
    ylabel('源距 (m)');
    title('DAS应变率数据');
    colorbar;
    colormap(seismic_colormap());
    
    subplot(1,2,2);
    if num_das_points >= 3
        plot(time_axis*1e6, das_data(:,2), 'k-', 'LineWidth', 1.5);
        xlabel('时间 (μs)');
        ylabel('应变率 (1/s)');
        title(sprintf('第2个DAS标距点波形 (源距: %.2fm)', source_distances(2)));
        grid on;
    end
    
    % 图3：检波器与DAS对比
    figure('Name', '检波器与DAS对比', 'Position', [200 200 1200 800]);
    
    subplot(2,2,1);
    imagesc(time_axis*1e6, source_distances, receiver_data_x');
    xlabel('时间 (μs)');
    ylabel('源距 (m)');
    title('检波器X方向速度');
    colorbar;
    colormap(seismic_colormap());
    
    subplot(2,2,2);
    imagesc(time_axis*1e6, source_distances, das_data');
    xlabel('时间 (μs)');
    ylabel('源距 (m)');
    title('DAS应变率');
    colorbar;
    colormap(seismic_colormap());
    
    % 单道对比
    subplot(2,1,2);
    if num_receivers >= 3
        plot(time_axis*1e6, receiver_data_x(:,2)/max(abs(receiver_data_x(:,2))), 'r-', 'LineWidth', 1.5); hold on;
        plot(time_axis*1e6, das_data(:,2)/max(abs(das_data(:,2))), 'b-', 'LineWidth', 1.5);
        xlabel('时间 (μs)');
        ylabel('归一化振幅');
        title(sprintf('第2个检波点：检波器vs DAS (源距: %.2fm)', source_distances(2)));
        legend('检波器X方向(归一化)', 'DAS应变率(归一化)', 'Location', 'best');
        grid on;
    end
end

%% ========================================================================
%  第四部分：频散分析
%% ========================================================================

% 图4：频散分析
figure('Name', '频散分析', 'Position', [250 250 1200 600]);

% 计算频谱
subplot(1,2,1);
if num_receivers >= 2
    % 选择第2个检波器进行频谱分析
    signal = receiver_data_x(:,2);
    
    % 计算频谱
    N = length(signal);
    freq = (0:N-1) * (1/dt) / N;
    freq = freq(1:floor(N/2));  % 只取正频率部分
    
    spectrum = abs(fft(signal));
    spectrum = spectrum(1:floor(N/2));
    
    plot(freq/1000, 20*log10(spectrum/max(spectrum)), 'b-', 'LineWidth', 1.5);
    xlabel('频率 (kHz)');
    ylabel('幅度 (dB)');
    title('第2个检波器频谱');
    grid on;
    xlim([0 min(20, max(freq)/1000)]);
    
    % 标记主频率
    hold on;
    plot([f0/1000 f0/1000], ylim, 'r--', 'LineWidth', 2);
    text(f0/1000, -10, sprintf('主频: %.0f Hz', f0), 'Color', 'red');
end

% 相速度分析
subplot(1,2,2);
if num_receivers >= 3
    % 计算相速度（简单的时差分析）
    dt_sample = dt;
    dz_receiver = receiver_spacing;
    
    % 找到第一个和第三个检波器的峰值时间
    [~, peak1] = max(abs(receiver_data_x(:,1)));
    [~, peak3] = max(abs(receiver_data_x(:,3)));
    
    time_diff = (peak3 - peak1) * dt_sample;
    distance_diff = 2 * dz_receiver;  % 第1到第3个检波器的距离
    
    if time_diff > 0
        apparent_velocity = distance_diff / time_diff;
        
        plot(source_distances(1:3), [peak1 NaN peak3]*dt_sample*1e6, 'ro-', 'LineWidth', 2, 'MarkerSize', 8);
        xlabel('源距 (m)');
        ylabel('峰值到达时间 (μs)');
        title(sprintf('波速分析\n表观速度: %.0f m/s', apparent_velocity));
        grid on;
        
        % 添加理论速度线
        hold on;
        theoretical_time = source_distances / vp_fluid * 1e6;  % 转换为微秒
        plot(source_distances, theoretical_time, 'b--', 'LineWidth', 1.5);
        legend('数值结果', '理论值', 'Location', 'best');
        
        % 显示频散信息
        text(0.1, 0.9, sprintf('理论速度: %.0f m/s', vp_fluid), 'Units', 'normalized');
        text(0.1, 0.8, sprintf('频散误差: %.2e', dispersion_error), 'Units', 'normalized');
    end
end

%% ========================================================================
%  第五部分：震源子波分析
%% ========================================================================

% 图5：震源子波分析
figure('Name', '震源子波分析', 'Position', [300 300 1000 600]);

subplot(1,2,1);
plot(time_axis*1e6, src, 'k-', 'LineWidth', 1.5);
xlabel('时间 (μs)');
ylabel('振幅');
title(sprintf('Ricker子波 (主频: %.0f Hz)', f0));
grid on;

subplot(1,2,2);
% 震源频谱
N_src = length(src);
freq_src = (0:N_src-1) * (1/dt) / N_src;
freq_src = freq_src(1:floor(N_src/2));

spectrum_src = abs(fft(src));
spectrum_src = spectrum_src(1:floor(N_src/2));

plot(freq_src/1000, spectrum_src/max(spectrum_src), 'k-', 'LineWidth', 1.5);
xlabel('频率 (kHz)');
ylabel('归一化振幅');
title('震源频谱');
grid on;
xlim([0 min(20, max(freq_src)/1000)]);

% 标记主频率
hold on;
plot([f0/1000 f0/1000], [0 1], 'r--', 'LineWidth', 2);
text(f0/1000, 0.8, sprintf('主频: %.0f Hz', f0), 'Color', 'red');

%% ========================================================================
%  辅助函数：地震配色方案
%% ========================================================================

function cmap = seismic_colormap()
    % 创建地震数据常用的红白蓝配色方案
    n = 64;
    r = [ones(n/2,1); linspace(1,0,n/2)'];
    g = [linspace(0,1,n/2)'; linspace(1,0,n/2)'];
    b = [linspace(0,1,n/2)'; ones(n/2,1)];
    cmap = [r g b];
end

fprintf('\n=== 画图完成 ===\n');
fprintf('已生成以下图形：\n');
fprintf('1. 检波器X、Y方向速度和压力数据\n');
if enable_das
    fprintf('2. DAS应变率数据\n');
    fprintf('3. 检波器与DAS对比\n');
end
fprintf('4. 频散分析\n');
fprintf('5. 震源子波分析\n');
fprintf('请查看各个图形窗口的结果。\n');
